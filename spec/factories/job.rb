# frozen_string_literal: true

FactoryBot.define do
  factory :job do
    sequence :number do |n|
      "#{order.number}-#{n}"
    end

    date { 2.days.from_now }
    association :order, factory: :order
    user { order.user }
    service { order.service }
    coordinates { order.coordinates }
    address { order.address }
    subscription { order.subscription }
    must_generate_nf { order.must_generate_nf }
    has_pets { order.has_pets }

    factory :cleaning_job do
      cleaning
    end

    trait :cleaning do
    end

    factory :cleaning_subscription_job do
      cleaning_subscription
    end

    trait :cleaning_subscription do
      association :order, factory: %i[order cleaning_subscription]
      subscription { true }
    end

    factory :express_cleaning_job do
      express_cleaning
    end

    trait :express_cleaning do
      association :order, factory: %i[order express_cleaning]
    end

    trait :with_unscheduled_period do
      date { 2.days.from_now.change(hour: [9, 12, 16, 20].sample, min: 1) }
    end

    factory :business_cleaning_job do
      business_cleaning
    end

    trait :business_cleaning do
      association :order, factory: %i[order business_cleaning]
    end

    factory :heavy_cleaning_job do
      heavy_cleaning
    end

    trait :heavy_cleaning do
      association :order, factory: %i[order heavy_cleaning]
    end

    factory :pre_moving_cleaning_job do
      pre_moving_cleaning
    end

    trait :pre_moving_cleaning do
      association :order, factory: %i[order pre_moving_cleaning]
    end

    factory :remodeling_cleaning_job do
      remodeling_cleaning
    end

    trait :remodeling_cleaning do
      association :order, factory: %i[order remodeling_cleaning]
    end

    factory :ironing_job do
      ironing
    end

    trait :ironing do
      association :order, factory: %i[order ironing]
    end

    factory :furniture_assembly do
      furniture_assembly
    end

    trait :furniture_assembly do
      association :order, factory: %i[order furniture_assembly]
    end

    trait :furniture_assembly_with_job_tasker do
      association :order, factory: %i[order furniture_assembly]

      job_taskers { [build(:job_tasker, tasker: nil, work_time: 1.0)] }
    end

    trait :with_one_job_tasker do
      transient do
        tasker { create(:tasker) }

        payout { 90 }
        user_payout { 80 }
        discount { 0 }
        final_payout { 0 }
        work_time { 4.0 }
        bonus_payout { 0 }
        bonus_type { :none }
      end

      job_taskers do
        build_list :job_tasker, 1,
                   tasker: tasker,
                   payout: payout,
                   user_payout: user_payout,
                   discount: discount,
                   final_payout:,
                   work_time:,
                   bonus_payout: bonus_payout,
                   bonus_type: bonus_type
      end
    end

    trait :with_job_taskers do
      job_taskers { [build(:job_tasker), build(:job_tasker)] }
    end

    trait :with_one_empty_job_tasker do
      job_taskers { [build(:job_tasker, tasker_id: nil)] }
    end

    trait :with_empty_job_taskers do
      job_taskers { [build(:job_tasker), build(:job_tasker)] }
    end

    trait :with_job_tasker do
      transient do
        tasker { create :tasker } # rubocop:disable FactoryBot/FactoryAssociationWithStrategy
        work_time { 4.0 }
      end

      state { :assigned }
      job_taskers { [build(:job_tasker, tasker:, work_time:)] }
    end

    trait :with_job_tasker_and_without_tasker do
      job_taskers { [build(:job_tasker, tasker: nil)] }
    end

    trait :paid do
      state { :paid }
    end

    trait :invoiced do
      state { :invoiced }
    end

    trait :pending do
      state { :pending }
    end

    trait :cancelled do
      state { :cancelled }
    end

    trait :on_hold do
      state { :on_hold }
    end

    trait :assigned do
      state { :assigned }
      job_taskers { [build(:job_tasker, payout: 90)] }
    end

    trait :completed do
      state { :completed }
      job_taskers { [build(:job_tasker, payout: 90)] }
    end

    trait :completed_with_tasker do
      transient do
        tasker { create(:tasker) }
      end
      state { :completed }
      job_taskers { [build(:job_tasker, payout: 90, tasker: tasker)] }
    end

    trait :skipped do
      state { :skipped }
    end

    trait :creditable do
      completed
      date { 8.hours.ago }
    end

    trait :chatable do
      with_job_tasker
      date { 20.hours.from_now }
    end

    factory :creditable_job do
      creditable
    end

    factory :uncreditable_job do
      completed
      date { 1.hour.ago }
    end

    trait :critical do
      critical_type { :no_show }
    end

    trait :with_cash_payment do
      association :order, factory: %i[order with_cash]
    end

    trait :with_optionals do
      optionals { { ironing: true, wash_windows: true, clean_cabinets: false } }
    end

    trait :with_sendbird_integration do
      integrations { { sendbird: { id: "job:#{id}", created_at: 1.hour.ago, updated_at: 1.hour.ago } } }
    end
  end
end
