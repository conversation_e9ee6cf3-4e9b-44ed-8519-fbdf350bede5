# frozen_string_literal: true

require 'sinatra/contrib'

require_relative 'sendbird/webhooks/group_channel/changed'
require_relative 'sendbird/webhooks/group_channel/create'
require_relative 'sendbird/webhooks/group_channel/user_joined'
require_relative 'sendbird/webhooks/user/create'

module Api
  # Sendbird namespace
  class Sendbird < Sinatra::Base
    VALID_CATEGORIES = [
      'group_channel:changed', 'group_channel:create', 'group_channel:user_joined', 'user:create'
    ].freeze

    VALID_TARGETS = %w[job offer].freeze

    register Sinatra::Contrib

    respond_to :json

    namespace '/sendbird' do
      post('/') do
        halt(401, 'Unauthorized') if invalid_request?

        halt(*Webhooks::User::Create.new(params).process) if params['category'] == 'user:create'
        halt(*Webhooks::GroupChannel::Create.new(params).process) if params['category'] == 'group_channel:create'
        halt(*Webhooks::GroupChannel::Changed.new(params).process) if params['category'] == 'group_channel:changed'

        update_target_integrations(params['channel']) if params['category'].starts_with?('group_channel:')

        { message: 'Data was succefully updated.' }.to_json
      end
    end

    private

    def raw_params
      @raw_params ||= request.body.read
    end

    def params
      JSON.parse(raw_params)
    end

    def channel_url
      @channel_url ||= params.dig('channel', 'channel_url')
    end

    def target_id
      @target_id ||= parse_id(channel_url)
    end

    def target_model
      reference = channel_url.split('_').first

      raise "Not a valid target: #{reference}" unless reference.in? VALID_TARGETS

      reference.camelize.constantize
    end

    def target
      return if target_id.blank?

      @target ||= target_model.find(target_id)
    end

    def parse_id(raw_id)
      return '' if raw_id.blank?

      return raw_id.sub(/^job_/, '') if raw_id.starts_with?('job_')

      return raw_id.sub(/^offer_/, '') if raw_id.starts_with?('offer_')

      return raw_id.sub(/^tasker:/, '') if tasker_user?

      raw_id.sub(/^user:/, '')
    end

    def invalid_request?
      return true unless params['category'].in? VALID_CATEGORIES
      return true unless request.env['HTTP_USER_AGENT'] == 'SendBird'
      return true unless request.env['CONTENT_TYPE'] == 'application/json'

      false
    end

    def update_target_integrations(channel_params)
      halt(404, 'Unable to find target.') unless target

      case params['category']
      when 'group_channel:changed'
        change_target_integration(JSON.parse(channel_params['data']))
      when 'group_channel:user_joined'
        tasker_user? ? tasker_joined_chat : user_joined_chat
      end
    end

    def change_target_integration(change_params = {})
      return if target.integrated_with?(:sendbird) && change_params.blank?

      frozen = change_params.blank? ? false : change_params['frozen']

      return target.integrated_with!(:sendbird, { frozen: }) unless target.integrated_with? :sendbird

      binding.pry
      target.integrations[:sendbird][:frozen] = frozen
      target.save
    end

    def user_id
      parse_id(params.dig('user', 'user_id'))
    end

    def tasker_user?
      raw_id = params.dig('user', 'user_id')

      return false unless raw_id

      raw_id.starts_with?('tasker')
    end

    def tasker_joined_chat
      return unless target.instance_of? Job

      change_target_integration

      job_tasker = target.job_taskers.find_by(tasker_id: user_id)

      return unless job_tasker

      job_tasker.integrated_with! :sendbird, { tasker_id: user_id }
    end

    def user_joined_chat
      return unless target.instance_of? Job

      change_target_integration

      target.integrations[:sendbird][:user_joined_at] = Time.current.iso8601
      target.save
    end
  end
end
