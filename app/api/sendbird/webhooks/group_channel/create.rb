# frozen_string_literal: true

module Api
  class Sendbird < Sinatra::Base
    module Webhooks
      module GroupChannel
        # Processor for Sendbird 'group_channel:create' webhook.
        #
        class Create
          attr_reader :params

          def initialize(params)
            @params = params
          end

          def process
            return if entity.integrated_with?(:sendbird)

            entity.integrated_with!(:sendbird, frozen: false)

            [200, { message: 'Data was succefully updated.' }.to_json]
          end

          private

          # Extracts collection and id from the raw `channel_url`.
          #
          # Patterns are:
          # - job_68b1a9ab12dfd5037b0811f0
          # - offer_68b1a9ab12dfd5037b0811f0
          # - order_tasker_68b1a9ab12dfd5037b0811f0
          #
          def entity
            raw_id = params.dig('channel', 'channel_url')
            matches = raw_id.match(/^(?<entity>[a-z_]+)_(?<id>\w+)$/)
            return unless matches

            entity = matches[:entity].singularize.camelize.constantize
            entity.find(matches[:id])
          end
        end
      end
    end
  end
end
